/* ===== DaisyTreeItem项目样式 ===== */
.daisy-tree-item {
    flex-direction: row;
    align-items: center;
    width: 100%;
    min-height: auto;
    background-color: transparent;
    border-width: 1px;
    position: relative;
}

.daisy-tree-item:hover {
    background-color: var(--daisy-base-200);
}

.daisy-tree-item.selected {
    background-color: var(--daisy-primary);
    color: var(--daisy-primary-content);
}

.daisy-tree-item.selected:hover {
    background-color: var(--daisy-primary-focus);
}

.daisy-tree-item.focus {
    background-color: var(--daisy-base-300);
}

/* ===== 容器结构 ===== */
.daisy-tree-item #container {
    flex: 1;
    flex-direction: row;
    align-items: center;
}

.daisy-tree-item #content {
    flex: 1;
    flex-direction: row;
    align-items: center;
    min-height: 20px;
}

.daisy-tree-item #main-button {
    flex: 1;
    background-color: transparent;
    border-width: 1px;
    -unity-text-align: middle-left;
    flex-direction: row;
    align-items: center;
    min-height: 20px;
    padding: 0;
}

.daisy-tree-item #main-button:hover {
    background-color: transparent;
}

/* ===== 缩进容器 ===== */
.daisy-tree-item #indent-container {
    flex-direction: row;
    align-items: center;
    flex-shrink: 0;
}

.daisy-tree-item .tree-indent {
    width: 16px;
    height: 20px;
    flex-shrink: 0;
    position: relative;
}

/* ===== 图标 ===== */
.daisy-tree-item #icon {
    width: 14px;
    height: 14px;
    margin-right: 4px;
    color: var(--daisy-base-content);
    flex-shrink: 0;
    -unity-text-align: middle-center;
}

.daisy-tree-item.selected #icon {
    color: var(--daisy-primary-content);
}

/* ===== 文本 ===== */
.daisy-tree-item #text {
    flex: 1;
    color: var(--daisy-base-content);
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    -unity-text-align: middle-left;
}

.daisy-tree-item.selected #text {
    color: var(--daisy-primary-content);
}

/* ===== 操作按钮容器 ===== */
.daisy-tree-item #actions-container {
    flex-direction: row;
    align-items: center;
    margin-left: 4px;
    flex-shrink: 0;
}

.daisy-tree-item .tree-action-button {
    width: 20px;
    height: 20px;
    background-color: transparent;
    border-width: 1px;
    border-radius: var(--daisy-rounded-sm);
    margin-left: 1px;
    align-items: center;
    justify-content: center;
    color: var(--daisy-base-content);
    opacity: 0.7;
    transition: all 0.2s ease;
}

.daisy-tree-item .tree-action-button:hover {
    background-color: var(--daisy-base-300);
    opacity: 1;
}

.daisy-tree-item.selected .tree-action-button {
    color: var(--daisy-primary-content);
}

.daisy-tree-item.selected .tree-action-button:hover {
    background-color: var(--daisy-primary-focus);
}

/* ===== 层级深度样式 ===== */
.daisy-tree-item.depth-0 #indent-container {
    margin-left: 0;
}

.daisy-tree-item.depth-1 #indent-container {
    margin-left: 16px;
}

.daisy-tree-item.depth-2 #indent-container {
    margin-left: 32px;
}

.daisy-tree-item.depth-3 #indent-container {
    margin-left: 48px;
}

.daisy-tree-item.depth-4 #indent-container {
    margin-left: 64px;
}

.daisy-tree-item.depth-5 #indent-container {
    margin-left: 80px;
}





/* ===== 深色主题 ===== */
.daisy-tree.dark .daisy-tree-item:hover {
    background-color: #404040;
}

.daisy-tree.dark .daisy-tree-item.selected {
    background-color: var(--daisy-primary);
}

.daisy-tree.dark .daisy-tree-item.focus {
    background-color: #505050;
}

.daisy-tree.dark .daisy-tree-item #icon {
    color: #ffffff;
}

.daisy-tree.dark .daisy-tree-item #text {
    color: #ffffff;
}

.daisy-tree.dark .daisy-tree-item .tree-action-button {
    color: #ffffff;
}

.daisy-tree.dark .daisy-tree-item .tree-action-button:hover {
    background-color: #505050;
}

/* ===== 尺寸变体 ===== */
.daisy-tree.xs .daisy-tree-item {
    min-height: 24px;
    padding: 2px 4px;
}

.daisy-tree.xs .daisy-tree-item #text {
    font-size: 12px;
}

.daisy-tree.xs .daisy-tree-item .tree-action-button {
    width: 20px;
    height: 20px;
}

.daisy-tree.sm .daisy-tree-item {
    min-height: 28px;
    padding: 3px 6px;
}

.daisy-tree.sm .daisy-tree-item #text {
    font-size: 13px;
}

.daisy-tree.sm .daisy-tree-item .tree-action-button {
    width: 22px;
    height: 22px;
}

.daisy-tree.lg .daisy-tree-item {
    min-height: 36px;
    padding: 6px 12px;
}

.daisy-tree.lg .daisy-tree-item #text {
    font-size: 15px;
}

.daisy-tree.lg .daisy-tree-item .tree-action-button {
    width: 28px;
    height: 28px;
}

.daisy-tree.xl .daisy-tree-item {
    min-height: 40px;
    padding: 8px 16px;
}

.daisy-tree.xl .daisy-tree-item #text {
    font-size: 16px;
}

.daisy-tree.xl .daisy-tree-item .tree-action-button {
    width: 32px;
    height: 32px;
}

/* ===== 修饰符 ===== */
.daisy-tree.compact .daisy-tree-item {
    min-height: 20px;
    padding: 1px 3px;
}

.daisy-tree.compact .daisy-tree-item .tree-indent {
    width: 12px;
}

.daisy-tree.compact .daisy-tree-item .tree-action-button {
    width: 18px;
    height: 18px;
}

.daisy-tree.bordered .daisy-tree-item {
}

.daisy-tree.rounded .daisy-tree-item {
    border-radius: var(--daisy-rounded-md);
    margin: 1px;
}

.daisy-tree.shadow .daisy-tree-item.selected {
}

/* ===== 状态样式 ===== */
.daisy-tree-item.disabled {
    opacity: 0.5;
}

.daisy-tree-item.loading #icon {
}

@keyframes loading-spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.daisy-tree-item.highlighted {
    background-color: var(--daisy-accent);
    color: var(--daisy-accent-content);
}

.daisy-tree-item.highlighted:hover {
    background-color: var(--daisy-accent-focus);
}



/* ===== 拖拽状态 ===== */
.daisy-tree.drag-drop-enabled .daisy-tree-item.dragging {
    opacity: 0.5;
    scale: 0.98
}

.daisy-tree.drag-drop-enabled .daisy-tree-item.drop-target {
    background-color: var(--daisy-accent);
    border-width: 2px;
}

.daisy-tree.drag-drop-enabled .daisy-tree-item.drop-hover {
    background-color: var(--daisy-accent-focus);
}

/* ===== 搜索高亮 ===== */
.daisy-tree-item.search-match #text {
    background-color: var(--daisy-warning);
    color: var(--daisy-warning-content);
    padding: 0 2px;
    border-radius: var(--daisy-rounded-sm);
}

.daisy-tree-item.search-highlight {
    background-color: var(--daisy-info);
    color: var(--daisy-info-content);
}

/* ===== 特殊状态 ===== */
.daisy-tree-item.active {
    background-color: var(--daisy-primary);
    color: var(--daisy-primary-content);
}

.daisy-tree-item.active:hover {
    background-color: var(--daisy-primary-focus);
}

.daisy-tree-item.error {
    background-color: var(--daisy-error);
    color: var(--daisy-error-content);
}

.daisy-tree-item.error:hover {
    background-color: var(--daisy-error-focus);
}

.daisy-tree-item.warning {
    background-color: var(--daisy-warning);
    color: var(--daisy-warning-content);
}

.daisy-tree-item.warning:hover {
    background-color: var(--daisy-warning-focus);
}

.daisy-tree-item.success {
    background-color: var(--daisy-success);
    color: var(--daisy-success-content);
}

.daisy-tree-item.success:hover {
    background-color: var(--daisy-success-focus);
}

.daisy-tree-item.info {
    background-color: var(--daisy-info);
    color: var(--daisy-info-content);
}

.daisy-tree-item.info:hover {
    background-color: var(--daisy-info-focus);
}


/* ===== 可访问性 ===== */
.daisy-tree-item:focus {
}

.daisy-tree-item.selected:focus {
}
